@import url('https://fonts.googleapis.com/css2?family=Noto+Sans:wght@100..900&family=Noto+Serif:wght@100..900&family=Noto+Sans+Mono:wght@100..900&family=Noto+Sans+Display:wght@100..900&display=swap');

/* font-family: 'Noto Sans', sans-serif; */
/* font-family: 'Noto Serif', serif; */

/* font-style: italic; */
/* font-style: normal; */

/* The font-stretch property accepts values like
ultra-condensed
extra-condensed
condensed
semi-condensed
normal
semi-expanded
expanded
extra-expanded
ultra-expanded
*/

/* Font Weight Values:
100: Thin
200: Extra Light
300: Light
400: Normal
500: Medium
600: Semi-Bold
700: Bold
800: Extra Bold
900: Black
*/

:root {
    font-family: 'Noto Sans', sans-serif;
    font-size: 16px;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;

    /* DARK --color-text-dark value #FEBA9C is used in js*/
    /* --color-background-dark: #000000;
    --color-background-middle: #464B53;
    --color-background-bright: #2d2d2d;
    --color-text-title: #FD6630;
    --color-text-subtitle: #FEBA9C;
    --color-text-black: #F0DAC6;
    --color-text-dark: #FEBA9C;
    --color-text-middle: #FF6631;
    --color-text-bright: #F0DAC6;
    --color-text-brighter: #F0DAC6;
    --color-border-middle: rgb(0, 0, 0);
    --color-input-lines: #FEBA9C;
    --color-hr-lines: #6b4400;
    --color-selected-green: hsl(55, 96%, 38%);
    --color-selected-red: #ff6161;
    --color-disabled: #c8bbb5;
    --color-list-background: rgba(250, 167, 0, 0.07);
    --color-error-title: #e20000;
    --padding-border-middle: 0px;
    --width-border-middle: 5px;
    --radius-border-middle: 10px; */


    /* ORANGE */
    --color-background-dark: #472c07;
    --color-background-middle: #d89c52;
    --color-background-bright: #f6e1c5;
    --color-text-title: #000000;
    --color-text-subtitle: #000000;
    --color-text-black: #000000;
    --color-text-dark: #8b4f06;
    --color-text-middle: #945200;
    --color-text-bright: rgb(222, 203, 177);
    --color-text-brighter: #f6e1c5;
    --color-border-middle: #e5ad63;
    --color-input-lines: #d89c52;
    --color-hr-lines: #6b4400;
    --color-selected-green: hsl(55, 96%, 38%);
    --color-selected-red: #bc2600;
    --color-disabled: #6f6f6f;
    --color-list-background: rgba(250, 167, 0, 0.07);
    --color-error-title: #ff3700;
    --padding-border-middle: 4px;
    --width-border-middle: 4px;
    --radius-border-middle: 12px;

    /* BROWN */
    /* --color-background-dark: #4a3f2c;
    --color-background-middle: #b19c77;
    --color-background-bright: #e9e3d8;
    --color-text-title: #000000;
    --color-text-subtitle: #000000;
    --color-text-black: #000000;
    --color-text-dark: #6b4400;
    --color-text-middle: #b19c77;
    --color-text-bright: #d7d2c9;
    --color-text-brighter: #e9e3d8;
    --color-border-middle: #c6b18e;
    --color-input-lines: #6b4400;
    --color-hr-lines: #6b4400;
    --color-selected-green: hsl(55, 96%, 38%);
    --color-selected-red: #ff3300;
    --color-disabled: #6f6f6f;
    --color-list-background: rgba(250, 167, 0, 0.07);
    --color-error-title: #a02300;
    --padding-border-middle: 4px;
    --width-border-middle: 4px;
    --radius-border-middle: 12px; */

    /* PURPLE */
    /* --color-background-dark: #663b18;
    --color-background-middle: #e0dbcd;
    --color-background-bright: #f6f5ee;
    --color-text-title: #000000;
    --color-text-subtitle: #000000;
    --color-text-black: #000000;
    --color-text-dark: #7c3800;
    --color-text-middle: #615637;
    --color-text-bright: #f6e1c5;
    --color-text-brighter: #f6e1c5;
    --color-border-middle: rgba(102, 59, 24, 0.2);
    --color-input-lines: #994500;
    --color-hr-lines: #6b4400;
    --color-selected-green: hsl(55, 96%, 38%);
    --color-selected-red: #ff3300;
    --color-disabled: #6f6f6f;
    --color-list-background: rgba(250, 167, 0, 0.07);
    --color-error-title: #bc2600;
    --padding-border-middle: 0px;
    --width-border-middle: 5px;
    --radius-border-middle: 10px; */
    }

html, body {
    font-family: 'Noto Sans', sans-serif;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;
    font-size: 18px;
    background-color: var(--color-background-middle);
    color: var(--color-text-black);
    height: 100%;
    box-sizing: border-box;
    margin: 0px;
    padding: 0px;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active{
    font-family: 'Noto Sans', sans-serif;
    font-weight: 400;
    font-stretch: normal;
    font-style: normal;
    font-size: 18px;
    -webkit-background-clip: text;
    -webkit-text-fill-color: var(--color-text-black);
    transition: background-color 5000s ease-in-out 0s;
    box-shadow: inset 0 0 20px 20px rgba(35, 35, 35, 0);
}

.Site {
    display: flex;
    min-height: 100vh;
    flex-direction: column;
}

.container {
    width: clamp(98%, 92%, 1200px);
    margin: auto;
    height: 100%;
}

hr.footer-hr {
    display: block;
    height: 1px;
    border: 0;
    border-top: 1px solid #ccc;
    margin: 0;
    padding: 0;
    border-color: var(--color-text-dark);
}

a {
    color: var(--color-text-dark);
}

.fa-square-facebook{
    font-size: 22px;
}
.fa-linkedin{
    font-size: 22px;
}
.fa-envelope{
    font-size: 25px;
}

.navbarlogo {
    padding-right: 10px;
    width: 24px;
    height: 24px;
}

.footerlogo {
    padding-right: 10px;
    width: 24px;
    height: 24px;
}

.content-div{
    width: auto;
    max-width: 820px;
    margin: 60px auto;
    border-style: solid;
    border-color: var(--color-border-middle);
    border-width: var(--width-border-middle);
    border-radius: var(--radius-border-middle);
    padding: var(--padding-border-middle);
}

.content-div.slidetransition {
  view-transition-name: slide-it;
}

@keyframes fadeIn {
    to {
    opacity: 1;
    }
}

@view-transition {
    navigation: auto;
}

@keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fade-out {
    to { opacity: 0; }
}

@keyframes slide-from-right {
    from { transform: translateX(90px); }
}

@keyframes slide-to-left {
    to { transform: translateX(-90px); }
}

::view-transition-old(slide-it) {
    animation:
    180ms cubic-bezier(0.4, 0, 1, 1) both fade-out,
    600ms cubic-bezier(0.4, 0, 0.2, 1) both slide-to-left;
}
::view-transition-new(slide-it) {
    animation:
    420ms cubic-bezier(0, 0, 0.2, 1) 90ms both fade-in,
    600ms cubic-bezier(.63,.7,.16,1.28) both slide-from-right;
}

::view-transition-old(simple-fade-slide) {
    animation: 300ms ease-out both fade-out;
}
::view-transition-new(simple-fade-slide) {
    animation:
        300ms ease-out both fade-in,
        600ms ease-out both slide-from-above;
}

@keyframes slide-from-above {
    from { transform: translateY(-30px); }
    to { transform: translateY(0); }
}

#errordiv {
  view-transition-name: simple-fade-slide;
}

[popover] {
  transition: opacity 0.2s, transform 0.2s, display 0.2s allow-discrete;
  opacity: 0;
  transform: translateY(48px);
  &:popover-open {
    opacity: 1;
    transform: none;
    @starting-style {
      & {
        opacity: 0;
        transform: translateY(-16px);
      }
    }
  }
}


input#menu {
    display: none;
}

.icon {
    width: 32px;
    height: 32px;
    position: relative;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translateY(-1px);
}

.menu {
    width: 26px;
    height: 2px;
    background-color: var(--color-text-bright);
    position: relative;
    transition: all 0.3s ease;
}

.menu::before,
.menu::after {
    content: '';
    position: absolute;
    width: 26px;
    height: 2px;
    background-color: var(--color-text-bright);
    left: 0;
    transition: all 0.3s ease;
}

.menu::before {
    top: -8px;
}

.menu::after {
    top: 8px;
}

#menu:checked + .icon .menu {
    background: transparent;
}

#menu:checked + .icon .menu::before {
    transform: rotate(45deg);
    top: 0;
}

#menu:checked + .icon .menu::after {
    transform: rotate(-45deg);
    top: 0;
}

.icon:hover .menu,
.icon:hover .menu::before,
.icon:hover .menu::after {
    background-color: greenyellow;
}




.dropdown {
    position: absolute;
    top: 40px;
    right: 0;
    width: auto;
    min-width: 150px;
    background-color: var(--color-background-dark);
    border: 4px solid var(--color-border-middle);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
    transition: max-height 0.3s ease, opacity 0.3s ease, transform 0.4s ease;
    display: inline-block;
    white-space: nowrap;
}

#menu:checked ~ .dropdown {
    max-height: 500px;
    opacity: 1;
    transform: translateY(0);
}

.dropdown-item {
    padding: 7px 25px 7px 10px;
    cursor: pointer;
    transition: opacity 0.3s ease, transform 0.3s ease;
    opacity: 0;
    transform: translateY(-10px);
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--color-background-middle);
}

.dropdown-item .icon i {
    display: inline-block;
    transform: translateY(2px);
}

.dropdown-item .icon {
    display: inline-flex;
    align-items: center;
    margin-right: 12px;
    font-size: 16px;
    color: var(--color-text-bright);
}

.dropdown-item .text {
    color: var(--color-text-bright);
    vertical-align: middle;
}

#menu:checked ~ .dropdown .dropdown-item {
    opacity: 1;
    transform: translateY(0);
}

#menu:checked ~ .dropdown .dropdown-item:nth-child(1) {
    transition-delay: 0.01s;
}
#menu:checked ~ .dropdown .dropdown-item:nth-child(2) {
    transition-delay: 0.05s;
}
#menu:checked ~ .dropdown .dropdown-item:nth-child(3) {
    transition-delay: 0.1s;
}
#menu:checked ~ .dropdown .dropdown-item:nth-child(4) {
    transition-delay: 0.15s;
}
.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover {
    background-color: var(--color-background-bright);
}
.dropdown-item:hover .text {
    color: var(--color-text-dark);
}
.dropdown-item:hover .icon {
    color: var(--color-text-dark);
}
