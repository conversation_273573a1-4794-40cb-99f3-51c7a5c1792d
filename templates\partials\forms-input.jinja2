<div>
  <style>
      me {
        position: relative;
        height: 95px;
        margin-top: 16px;
        margin-bottom: 16px;
        flex-basis: 100%;
      }

      me input {
        height: 32px;
        position: absolute;
        top: 30px;
        left: 0;
        right: 0;
        font-family: 'Noto Sans', sans-serif;
        font-size: 18px;
        color: var(--color-text-black);
        border: 0;
        z-index: 1;
        background-color: transparent;
        border-bottom: 1px solid var(--color-input-lines);
        border-radius: 0;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;

        &:focus {
            outline: 0;
            border-bottom: 1px solid var(--color-input-lines);

            &+.input-label {
                font-family: 'Noto Serif', serif;
                font-style: italic;
                font-size: 15px;
                color: var(--color-text-dark);
                top: 10px;
            }
        }

        &:not(:placeholder-shown):valid {
            border-bottom: 1px solid var(--color-selected-green);

            &+.input-label {
                font-family: 'Noto Serif', serif;
                font-style: italic;
                font-size: 15px;
                color: var(--color-text-dark);
                top: 10px;
            }
        }

        &:not(:placeholder-shown):invalid {
            border-bottom: 1px solid var(--color-selected-red);

            &+.input-label {
                font-family: 'Noto Serif', serif;
                font-style: italic;
                font-size: 15px;
                color: var(--color-text-dark);
                top: 10px;
            }
        }

        &:not(:placeholder-shown):not(:focus):invalid~.input-group__error {
            visibility: visible;
            opacity: 1;
        }

        /* Hide error on page load for fields with default values */
        &[data-touched="false"]:not(:focus)~.input-group__error {
            visibility: hidden;
            opacity: 0;
        }

        &:disabled {
            color: var(--color-disabled);
            border-bottom: 1px solid var(--color-disabled);
        }
        &:disabled + .input-label {
            color: var(--color-disabled);
        }
        &:disabled + .input-label.float-label {
            color: var(--color-disabled);
        }
      }

      me .input-group__error {
          position: absolute;
          top: 70px;
          left: 10px;
          color: var(--color-selected-red);
          display: block;
          visibility: hidden;
          opacity: 0;
          font-family: 'Noto Serif', serif;
          font-size: 14px;
          transition: all 0.3s ease-out;
      }

  </style>

  <input pattern="{{ pattern | default('') }}" placeholder=" " type="{{ type | default('text') }}" {% if value %}value="{{ value }}" data-touched="false"{% else %}data-touched="true"{% endif %} name="{{ namealwayschange }}" id="{{ namealwayschange }}" {% if required is not defined or required %} required{% endif %} onblur="this.setAttribute('data-touched', 'true')" oninput="this.setAttribute('data-touched', 'true')" />
  <label class="input-label">
    <style>
        me {
            position: absolute;
            top: 36px;
            left: 0;
            color: var(--color-text-black);
            transition: .15s ease;
        }
        .input-label:disabled, input:disabled + .input-label {
            var(--color-disabled);
        }
        input:disabled + .input-label.float-label {
            var(--color-disabled);
        }
    </style>
    {{ label | safe }}
  </label>

  <span class="input-group__error">{{ errormessage }}</span>
</div>


{# Here are the different input types you can use in HTML:

<input type="button">
<input type="checkbox">
<input type="color">
<input type="date">
<input type="datetime-local">
<input type="email">
<input type="file">
<input type="hidden">
<input type="image">
<input type="month">
<input type="number">
<input type="password">
<input type="radio">
<input type="range">
<input type="reset">
<input type="search">
<input type="submit">
<input type="tel">
<input type="text">
<input type="time">
<input type="url">
<input type="week">

#}


{# The value attribute specifies the value of an <input> element.
The value attribute is used differently for different input types:
For "button", "reset", and "submit" - it defines the text on the button
For "text", "password", and "hidden" - it defines the initial (default) value of the input field
For "checkbox", "radio", "image" - it defines the value associated with the input (this is also the value that is sent on submit) #}