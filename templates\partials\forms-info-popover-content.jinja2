<div>
  <style>
    me {
      all: unset;
      width: 400px;
      border: 1px solid var(--color-background-dark);
      border-radius: 11px;
      display: block;
      padding: 0;
      font-family: 'Noto Sans', sans-serif;
    }
  </style>

  {% if infotitle %}
  <div>
    <style>
      me {
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        background-color: var(--color-background-bright);
        color: var(--color-text-dark);
        height: 40px;
        text-align: center;
        line-height: 40px;
        vertical-align: middle;
        font-family: 'Noto Serif', serif;
        font-size: 20px;
        font-weight: 600;
        font-stretch: normal;
        font-style: normal;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 0 8px;
      }
    </style>
    {{ infotitle | safe }}
  </div>
  {% endif %}

  {% if infounit %}
  <div>
    {% if not infotitle %}
    <style>
      me {
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
      }
    </style>
    {% endif %}
    <style>
      me {
        background-color: var(--color-background-dark);
        color: var(--color-text-bright);
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 10px;
        font-size: 16px;
        font-weight: 300;
        font-stretch: normal;
        font-style: normal;
      }
    </style>
    <span>
      <style>
        me {
          color: var(--color-text-bright);
          font-size: 16px;
          font-weight: 100;
          font-stretch: normal;
          font-style: normal;
        }
      </style>
      Unit
    </span>
    <span>
      <style>
        me {
          text-align: center;
          line-height: 32px;
          vertical-align: middle;
          color: var(--color-text-brighter);
          font-size: 16px;
          font-weight: 500;
          font-stretch: normal;
          font-style: normal;
        }
      </style>
      {{ infounit | safe }}
    </span>
  </div>
  {% endif %}

  <div>
    {% if not infotitle and not infounit %}
    <style>
      me {
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
      }
    </style>
    {% endif %}
    {% if not inforange %}
    <style>
      me {
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
      }
    </style>
    {% endif %}
    <style>
      me{
        background-color: var(--color-background-bright);
        padding: 15px 10px;
        text-align: center;
        font-size: 17px;
        font-weight: 300;
        font-stretch: normal;
        font-style: normal;
        color: var(--color-text-black);
      }
    </style>
    {{ infotext | safe }}
  </div>

  {% if inforange %}
  <div>
    <style>
      me {
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
        background-color: var(--color-background-dark);
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 10px;
      }
    </style>
    <span>
      <style>
        me {
          color: var(--color-text-bright);
          font-size: 16px;
          font-weight: 100;
          font-stretch: normal;
          font-style: normal;
        }
      </style>
      Range
    </span>
    <span>
      <style>
        me {
          text-align: center;
          line-height: 32px;
          vertical-align: middle;
          color: var(--color-text-brighter);
          font-size: 16px;
          font-weight: 500;
          font-stretch: normal;
          font-style: normal;
        }
      </style>
      {{ inforange | safe }}
    </span>
  </div>
  {% endif %}

</div>